{"system_name": "今日头条差异化创作系统", "version": "2.0", "description": "集成热点发现、写作推荐、提示词库、知识库的全方位创作系统", "modules": {"hotspot_discovery": {"name": "热点发现引擎", "description": "实时发现和分析热点话题，提供独特角度", "nodes": [{"id": "trend_monitor", "type": "llm", "name": "趋势监测分析", "config": {"model": "gpt-4", "temperature": 0.3, "max_tokens": 2000, "prompt": "你是资深的趋势分析师和内容策略专家。请分析当前热点话题并提供独特视角：\n\n分析维度：\n1. 热点话题识别\n2. 竞争对手分析\n3. 独特角度挖掘\n4. 受众痛点分析\n5. 内容空白点发现\n\n输入话题：{{input_topic}}\n\n请完成以下分析：\n\n【热点等级评估】：\n- 热度指数：（1-10分）\n- 持续时间预测：\n- 竞争激烈度：\n- 内容饱和度：\n\n【竞争对手分析】：\n- 主流观点总结：\n- 内容同质化程度：\n- 未被充分讨论的角度：\n\n【独特角度挖掘】：\n1. 反向思维角度：\n2. 跨领域关联角度：\n3. 历史对比角度：\n4. 未来预测角度：\n5. 个人经历角度：\n\n【受众洞察】：\n- 核心关注点：\n- 情感需求：\n- 认知盲区：\n- 行为偏好：\n\n【内容机会点】：\n- 空白领域：\n- 深度挖掘方向：\n- 争议话题处理：\n- 价值观输出机会：\n\n要求：提供3-5个具体的差异化内容方向，确保独特性和价值性。"}}, {"id": "niche_finder", "type": "llm", "name": "细分领域发现", "config": {"model": "gpt-4", "temperature": 0.4, "max_tokens": 1500, "prompt": "你是细分市场专家。基于热点分析，发现细分创作机会：\n\n热点分析：{{trend_analysis}}\n\n请完成：\n\n【细分领域识别】：\n1. 垂直细分方向：\n2. 交叉领域机会：\n3. 新兴话题萌芽：\n4. 被忽视的群体：\n\n【差异化定位】：\n- 独特价值主张：\n- 目标受众画像：\n- 内容风格定位：\n- 竞争优势分析：\n\n【内容策略建议】：\n- 长期内容规划：\n- 系列文章主题：\n- 互动形式创新：\n- 个人IP打造方向：\n\n输出格式：为每个细分领域提供具体的执行建议和差异化策略。"}}]}, "writing_recommendation": {"name": "智能写作推荐", "description": "基于数据分析提供个性化写作建议", "nodes": [{"id": "style_analyzer", "type": "llm", "name": "写作风格分析器", "config": {"model": "gpt-4", "temperature": 0.3, "max_tokens": 1800, "prompt": "你是写作风格专家。分析并推荐独特的写作风格：\n\n用户信息：\n- 专业背景：{{user_background}}\n- 兴趣领域：{{interest_areas}}\n- 目标受众：{{target_audience}}\n- 现有风格：{{current_style}}\n\n请分析并推荐：\n\n【个人风格诊断】：\n- 当前风格特点：\n- 优势分析：\n- 改进空间：\n- 差异化潜力：\n\n【风格推荐方案】：\n\n方案一：【深度思辨型】\n- 特点：逻辑严密，观点犀利\n- 适用话题：社会现象、行业分析\n- 写作技巧：多层论证、案例对比\n- 差异化要素：独特视角+深度思考\n\n方案二：【故事叙述型】\n- 特点：情感丰富，代入感强\n- 适用话题：人物故事、生活感悟\n- 写作技巧：场景描述、情感渲染\n- 差异化要素：真实经历+情感共鸣\n\n方案三：【数据洞察型】\n- 特点：数据驱动，客观理性\n- 适用话题：趋势分析、市场研究\n- 写作技巧：图表解读、数据可视化\n- 差异化要素：专业分析+通俗解读\n\n方案四：【跨界融合型】\n- 特点：知识面广，连接能力强\n- 适用话题：跨领域分析、创新思维\n- 写作技巧：类比思维、知识迁移\n- 差异化要素：跨界视角+创新思维\n\n【个性化建议】：\n基于您的背景，推荐重点发展的风格方向和具体实施策略。"}}, {"id": "content_optimizer", "type": "llm", "name": "内容优化推荐", "config": {"model": "gpt-4", "temperature": 0.4, "max_tokens": 2000, "prompt": "你是内容优化专家。基于风格分析提供具体的内容优化建议：\n\n风格分析：{{style_analysis}}\n当前内容：{{current_content}}\n\n请提供优化建议：\n\n【结构优化】：\n- 开头优化：3种吸引读者的开头方式\n- 段落调整：逻辑流畅的段落安排\n- 结尾升华：3种有力的结尾方式\n\n【语言优化】：\n- 词汇选择：更有感染力的词汇替换\n- 句式变化：长短句搭配建议\n- 修辞手法：比喻、排比、反问等运用\n\n【观点优化】：\n- 独特视角：与众不同的观点表达\n- 深度挖掘：更深层次的思考角度\n- 价值输出：对读者的实际价值\n\n【互动优化】：\n- 提问设计：引发思考的问题\n- 情感共鸣：触动读者内心的表达\n- 行动引导：促进读者参与的方式\n\n【差异化要素】：\n- 个人标签：建立个人识别度的元素\n- 独特表达：专属的表达方式\n- 价值观输出：体现个人价值观的内容\n\n输出具体的修改建议和示例。"}}]}, "prompt_library": {"name": "专业提示词库", "description": "分类整理的高质量提示词库", "categories": {"深度分析类": {"社会现象分析": "你是资深社会观察家。请从以下角度深度分析{{topic}}：\n1. 现象本质：透过表象看本质\n2. 历史脉络：历史发展轨迹分析\n3. 多方视角：不同群体的观点\n4. 深层原因：根本原因挖掘\n5. 未来趋势：发展方向预测\n6. 个人思考：独特见解和判断\n\n要求：观点犀利、逻辑严密、有理有据、避免空泛。", "行业趋势分析": "你是{{industry}}行业的资深分析师。请深度分析{{topic}}：\n\n【现状梳理】：\n- 市场规模和增长趋势\n- 主要玩家和竞争格局\n- 技术发展水平\n- 政策环境影响\n\n【深度分析】：\n- 驱动因素分析\n- 阻碍因素识别\n- 机会与挑战并存\n- 商业模式创新\n\n【趋势预测】：\n- 短期发展方向（1-2年）\n- 中期变化趋势（3-5年）\n- 长期演进路径（5-10年）\n\n【投资建议】：\n- 值得关注的细分领域\n- 潜在的投资机会\n- 风险提示和规避\n\n要求：数据支撑、逻辑清晰、预测合理。", "技术影响分析": "你是技术哲学家。请分析{{technology}}对社会的深层影响：\n\n【技术本质】：\n- 技术原理和特点\n- 与传统技术的区别\n- 技术发展阶段\n\n【社会影响】：\n- 对生产方式的改变\n- 对生活方式的影响\n- 对社会关系的重塑\n- 对思维模式的冲击\n\n【哲学思考】：\n- 技术与人性的关系\n- 进步与风险的平衡\n- 自由与控制的博弈\n- 效率与温度的取舍\n\n【未来展望】：\n- 理想发展路径\n- 可能的风险点\n- 人类的应对策略\n\n要求：思考深刻、视野宏大、富有哲理。"}, "故事叙述类": {"人物故事": "你是故事大师。请讲述{{character}}的故事：\n\n【人物塑造】：\n- 基本信息和背景\n- 性格特点和价值观\n- 关键经历和转折点\n- 成长轨迹和变化\n\n【故事结构】：\n- 引人入胜的开头\n- 跌宕起伏的过程\n- 感人至深的高潮\n- 意味深长的结尾\n\n【细节描写】：\n- 生动的场景描述\n- 真实的对话还原\n- 细腻的心理刻画\n- 感人的情感表达\n\n【价值升华】：\n- 故事背后的意义\n- 对读者的启发\n- 普世价值的体现\n- 情感共鸣的触发\n\n要求：真实感人、细节丰富、情感饱满、有教育意义。", "经历分享": "你是生活智者。请分享关于{{experience}}的深刻经历：\n\n【背景铺垫】：\n- 时间地点人物\n- 当时的心境状态\n- 面临的具体情况\n\n【过程描述】：\n- 事件发展脉络\n- 关键决策时刻\n- 内心斗争过程\n- 行动和结果\n\n【感悟总结】：\n- 获得的启发\n- 改变的认知\n- 学到的道理\n- 成长的收获\n\n【价值传递】：\n- 对他人的建议\n- 可复制的经验\n- 避免的误区\n- 人生的感悟\n\n要求：真实可信、情感真挚、启发性强、贴近生活。"}, "创意表达类": {"观点评论": "你是独立思考者。请对{{topic}}发表独特观点：\n\n【观点立场】：\n- 明确的立场表态\n- 与主流观点的差异\n- 立场的理论基础\n\n【论证过程】：\n- 核心论点阐述\n- 支撑论据展示\n- 逻辑推理过程\n- 反驳可能质疑\n\n【创新视角】：\n- 跨领域的类比\n- 历史的对照\n- 未来的预测\n- 哲学的思辨\n\n【价值输出】：\n- 对读者的启发\n- 思维方式的改变\n- 行为指导意义\n- 社会价值体现\n\n要求：观点鲜明、论证有力、视角独特、有说服力。", "趋势预测": "你是未来学家。请预测{{field}}领域的发展趋势：\n\n【现状分析】：\n- 当前发展水平\n- 主要特征总结\n- 存在问题识别\n\n【驱动因素】：\n- 技术推动力\n- 市场需求变化\n- 政策环境影响\n- 社会文化因素\n\n【趋势预测】：\n- 短期变化（1-2年）\n- 中期发展（3-5年）\n- 长期演进（5-10年）\n- 颠覆性可能\n\n【影响分析】：\n- 对个人的影响\n- 对行业的冲击\n- 对社会的改变\n- 应对策略建议\n\n要求：预测合理、分析深入、具有前瞻性。"}, "实用指南类": {"方法论分享": "你是实践专家。请分享{{skill}}的实用方法：\n\n【方法体系】：\n- 核心理论基础\n- 完整方法框架\n- 操作步骤详解\n- 注意事项提醒\n\n【实践案例】：\n- 成功案例分析\n- 失败教训总结\n- 关键要素识别\n- 可复制经验\n\n【进阶技巧】：\n- 高级技巧分享\n- 常见误区避免\n- 效率提升方法\n- 质量保证措施\n\n【个人建议】：\n- 学习路径规划\n- 练习方法推荐\n- 资源获取渠道\n- 持续改进策略\n\n要求：实用性强、可操作性高、有实际价值。", "问题解决": "你是问题解决专家。请提供{{problem}}的解决方案：\n\n【问题分析】：\n- 问题本质识别\n- 影响因素梳理\n- 根本原因挖掘\n- 解决难点分析\n\n【解决方案】：\n- 多种解决思路\n- 具体操作步骤\n- 资源需求分析\n- 风险评估预警\n\n【实施建议】：\n- 优先级排序\n- 时间安排规划\n- 效果评估标准\n- 调整优化机制\n\n【预防措施】：\n- 类似问题预防\n- 早期预警信号\n- 应急处理预案\n- 长期改进策略\n\n要求：方案可行、步骤清晰、考虑周全。"}}}, "knowledge_base": {"name": "专业知识库", "description": "分领域的专业知识和素材库", "domains": {"科技前沿": {"AI人工智能": {"核心概念": ["机器学习", "深度学习", "神经网络", "自然语言处理", "计算机视觉"], "发展历程": ["1956年达特茅斯会议", "专家系统时代", "机器学习兴起", "深度学习突破", "大模型时代"], "关键人物": ["图灵", "麦卡锡", "明斯基", "辛顿", "杨立昆", "吴恩达"], "重要事件": ["深蓝战胜卡斯帕罗夫", "AlphaGo战胜李世石", "GPT系列发布", "ChatGPT爆火"], "应用场景": ["智能客服", "自动驾驶", "医疗诊断", "金融风控", "内容创作"], "争议话题": ["AI威胁论", "就业替代", "数据隐私", "算法偏见", "AI伦理"], "未来趋势": ["AGI通用人工智能", "AI+各行业", "人机协作", "AI治理", "技术民主化"]}, "区块链技术": {"核心概念": ["分布式账本", "共识机制", "智能合约", "去中心化", "加密货币"], "技术特点": ["不可篡改", "透明公开", "去信任化", "全球化", "可编程"], "应用领域": ["数字货币", "供应链管理", "数字身份", "版权保护", "去中心化金融"], "发展阶段": ["比特币诞生", "以太坊智能合约", "De<PERSON>i爆发", "NFT热潮", "Web3概念"], "争议问题": ["能源消耗", "监管政策", "价格波动", "技术泡沫", "应用落地"]}}, "社会热点": {"教育改革": {"政策变化": ["双减政策", "职业教育", "高考改革", "素质教育", "教育公平"], "社会影响": ["培训机构整顿", "家长焦虑", "教师压力", "学生负担", "教育成本"], "国际对比": ["芬兰教育", "新加坡模式", "美国教育", "日本经验", "德国职教"], "未来方向": ["个性化教育", "AI+教育", "终身学习", "创新能力", "全人发展"]}, "就业市场": {"就业趋势": ["新兴职业", "技能需求", "远程办公", "灵活就业", "职业转型"], "热门行业": ["新能源", "生物医药", "人工智能", "新消费", "数字经济"], "挑战问题": ["就业压力", "技能错配", "年龄歧视", "性别差异", "地域不平衡"], "应对策略": ["技能提升", "终身学习", "创业创新", "政策支持", "社会保障"]}}}}, "differentiation_engine": {"name": "差异化引擎", "description": "打造独特个人IP和写作风格", "components": {"personal_branding": {"name": "个人品牌塑造", "strategies": [{"type": "专业标签", "description": "建立专业领域的权威形象", "implementation": ["深耕1-2个垂直领域", "持续输出专业观点", "建立独特分析框架", "形成个人方法论"]}, {"type": "观点标签", "description": "形成独特的观点和立场", "implementation": ["坚持独立思考", "敢于表达不同观点", "基于事实理性分析", "避免盲从和跟风"]}, {"type": "风格标签", "description": "建立独特的表达风格", "implementation": ["形成个人语言特色", "建立固定内容结构", "使用标志性表达方式", "保持风格一致性"]}]}, "content_innovation": {"name": "内容创新策略", "methods": [{"name": "跨界融合法", "description": "将不同领域的知识和观点进行创新性结合", "examples": ["用心理学分析商业现象", "用历史视角看科技发展", "用哲学思维解读社会问题", "用艺术角度理解技术创新"]}, {"name": "反向思维法", "description": "从相反角度思考问题，提供新颖视角", "examples": ["成功背后的失败因素", "热门趋势的潜在风险", "主流观点的盲区分析", "表面现象的深层原因"]}, {"name": "时空对比法", "description": "通过时间和空间的对比分析问题", "examples": ["古今对比看变化", "中外对比看差异", "过去现在未来的连接", "不同地区的经验借鉴"]}]}, "engagement_tactics": {"name": "读者互动策略", "techniques": [{"name": "情感共鸣", "methods": ["分享真实个人经历", "表达真实情感感受", "关注读者痛点需求", "提供情感价值支撑"]}, {"name": "思维启发", "methods": ["提出引人思考的问题", "分享独特思维模式", "提供新的认知框架", "激发读者深度思考"]}, {"name": "实用价值", "methods": ["提供具体行动指南", "分享实用工具方法", "给出明确建议方案", "帮助解决实际问题"]}]}}}, "quality_enhancement": {"name": "质量提升系统", "description": "确保内容的深度、创意和独特性", "dimensions": {"depth_analysis": {"name": "深度分析", "criteria": ["多层次分析框架", "本质问题挖掘", "系统性思考方式", "前瞻性观点预测"], "enhancement_methods": ["5W1H分析法", "SWOT分析框架", "因果关系梳理", "系统思维运用"]}, "creativity_boost": {"name": "创意提升", "techniques": ["头脑风暴法", "联想思维法", "类比推理法", "逆向思维法"], "innovation_areas": ["标题创新", "结构创新", "表达创新", "观点创新"]}, "uniqueness_check": {"name": "独特性检验", "evaluation_points": ["观点独特性", "角度新颖性", "表达原创性", "价值差异性"], "improvement_strategies": ["个人经历融入", "专业背景运用", "独特视角开发", "原创观点形成"]}}}}}