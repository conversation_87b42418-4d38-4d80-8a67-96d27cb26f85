{"workflow_name": "差异化今日头条创作工作流", "version": "2.0", "description": "集成热点发现、差异化分析、独特视角、个人IP打造的高级创作系统", "nodes": [{"id": "input_node", "type": "start", "name": "输入节点", "config": {"input_fields": [{"name": "topic", "type": "string", "description": "要创作的话题或关键词", "required": true}, {"name": "user_background", "type": "string", "description": "用户的专业背景和经历", "required": true}, {"name": "target_audience", "type": "string", "description": "目标读者群体", "required": true}, {"name": "content_style", "type": "string", "description": "期望的内容风格（深度分析/故事叙述/实用指南等）", "required": false}]}, "outputs": ["topic", "user_background", "target_audience", "content_style"]}, {"id": "trend_discovery", "type": "llm", "name": "热点发现与机会分析", "config": {"model": "gpt-4-turbo", "temperature": 0.3, "max_tokens": 3000, "prompt": "你是资深的内容策略专家和趋势分析师。请分析当前热点并发现差异化机会：\n\n输入话题：{{input_topic}}\n用户专业背景：{{user_background}}\n目标受众：{{target_audience}}\n\n请完成全面分析：\n\n【热点评估】：\n- 热度指数：（1-10分，考虑搜索量、讨论度、媒体关注度）\n- 生命周期：（萌芽期/成长期/成熟期/衰退期）\n- 竞争激烈度：（低/中/高，分析已有内容数量和质量）\n- 内容饱和度：（未饱和/半饱和/饱和/过饱和）\n\n【竞争对手分析】：\n- 主流观点总结：当前最常见的3-5个观点\n- 内容同质化程度：相似内容的比例和特点\n- 头部作者策略：知名作者的内容策略分析\n- 内容空白点：尚未被充分讨论的角度\n\n【差异化机会挖掘】：\n1. 反向思维角度：与主流观点相反的合理视角\n2. 跨领域关联：结合其他领域知识的独特视角\n3. 历史对比角度：通过历史对比提供新见解\n4. 未来预测角度：基于趋势的前瞻性分析\n5. 个人经历角度：结合个人专业背景的独特视角\n\n【受众洞察】：\n- 核心关注点：受众最关心的3-5个问题\n- 情感需求：受众的情感诉求（焦虑/好奇/认同等）\n- 认知盲区：受众可能不了解的重要信息\n- 行为偏好：受众的阅读和互动习惯\n\n【内容机会评估】：\n为每个差异化角度评估：\n- 独特性指数：（1-10分）\n- 可信度指数：（1-10分）\n- 吸引力指数：（1-10分）\n- 可执行性：（容易/中等/困难）\n\n【推荐策略】：\n基于分析结果，推荐最具潜力的2-3个内容方向，并说明理由。\n\n要求：分析深入、数据支撑、策略可行、具有前瞻性。"}, "inputs": ["topic", "user_background", "target_audience"], "outputs": ["trend_analysis"]}, {"id": "unique_angle_generator", "type": "llm", "name": "独特角度生成器", "config": {"model": "claude-3-sonnet", "temperature": 0.7, "max_tokens": 2500, "prompt": "你是创意思维专家。基于热点分析，生成独特的内容角度：\n\n热点分析：{{trend_analysis}}\n用户专业背景：{{user_background}}\n\n请运用以下创新方法生成独特角度：\n\n【跨界融合法】：\n结合用户专业背景，提供跨领域的独特视角：\n- 角度1：用{{user_background}}的专业知识分析该话题\n- 角度2：从{{user_background}}行业的发展历程看该现象\n- 角度3：将{{user_background}}的方法论应用到该问题\n\n【反向思维法】：\n从相反角度思考，提供颠覆性观点：\n- 成功背后的隐患分析\n- 热门趋势的潜在风险\n- 主流观点的局限性\n- 表面繁荣的深层问题\n\n【时空对比法】：\n通过时间和空间维度对比：\n- 历史上类似事件的对比分析\n- 不同国家/地区的处理方式对比\n- 过去-现在-未来的发展脉络\n- 理想状态与现实状态的差距\n\n【哲学思辨法】：\n从哲学高度思考问题本质：\n- 现象背后的本质规律\n- 价值观层面的深度思考\n- 人性角度的深入分析\n- 社会发展规律的体现\n\n【个人化视角】：\n结合个人经历和感悟：\n- 个人相关经历的启发\n- 从个人成长角度的思考\n- 个人价值观的体现\n- 对读者的人生启示\n\n输出格式：\n为每个角度提供：\n- 角度描述\n- 核心观点\n- 支撑论据\n- 独特价值\n- 预期效果\n\n要求：角度新颖、观点犀利、有理有据、具有启发性。"}, "inputs": ["trend_analysis", "user_background"], "outputs": ["unique_angles"]}, {"id": "personal_style_optimizer", "type": "llm", "name": "个人风格优化器", "config": {"model": "gpt-4-turbo", "temperature": 0.5, "max_tokens": 2200, "prompt": "你是个人IP打造专家。基于独特角度，优化个人写作风格：\n\n独特角度：{{unique_angles}}\n用户背景：{{user_background}}\n历史作品风格：{{historical_style}}\n\n请完成个人风格优化：\n\n【风格定位分析】：\n- 当前风格特点：分析现有风格的优势和特色\n- 差异化潜力：识别可以强化的独特元素\n- 改进空间：指出需要提升的方面\n- 竞争优势：相比其他作者的独特之处\n\n【个人标签设计】：\n1. 专业标签：基于专业背景的权威形象\n   - 标签描述：\n   - 体现方式：\n   - 强化策略：\n\n2. 观点标签：独特的观点和立场\n   - 标签描述：\n   - 核心观点：\n   - 表达方式：\n\n3. 风格标签：独特的表达风格\n   - 语言特色：\n   - 结构特点：\n   - 互动方式：\n\n【写作风格优化】：\n\n语言风格：\n- 词汇选择：更有个人特色的词汇偏好\n- 句式特点：形成标志性的句式结构\n- 修辞手法：常用的修辞技巧\n- 语气语调：独特的表达语气\n\n内容结构：\n- 开头方式：3种个人化的开头模式\n- 论证逻辑：独特的论证框架\n- 段落安排：个人化的内容组织方式\n- 结尾升华：标志性的结尾方式\n\n互动元素：\n- 提问方式：引发思考的独特提问\n- 情感表达：真实情感的自然流露\n- 价值观输出：个人价值观的巧妙融入\n- 读者连接：与读者建立情感纽带的方式\n\n【个人IP强化建议】：\n- 内容定位：在该领域的独特定位\n- 价值主张：为读者提供的独特价值\n- 差异化策略：与其他作者的区别化策略\n- 品牌建设：长期个人品牌建设规划\n\n要求：风格鲜明、个性突出、可执行性强、有长期价值。"}, "inputs": ["unique_angles", "user_background", "content_style"], "outputs": ["optimized_style"]}, {"id": "knowledge_integration", "type": "llm", "name": "知识库整合器", "config": {"model": "claude-3-opus", "temperature": 0.3, "max_tokens": 2800, "prompt": "你是知识整合专家。基于优化风格，整合相关知识素材：\n\n优化风格：{{optimized_style}}\n话题领域：{{topic_domain}}\n\n请从知识库中整合相关素材：\n\n【核心概念梳理】：\n- 基础概念：该话题的核心概念和定义\n- 关键术语：专业术语的准确解释\n- 理论框架：相关的理论体系和模型\n- 发展脉络：概念和理论的发展历程\n\n【事实数据收集】：\n- 权威数据：来自官方或权威机构的数据\n- 研究报告：相关的研究成果和报告\n- 案例素材：典型案例和实际应用\n- 对比数据：不同时期、地区的对比数据\n\n【观点素材整理】：\n- 专家观点：该领域专家的权威观点\n- 不同声音：多元化的观点和立场\n- 争议焦点：存在争议的核心问题\n- 前沿观点：最新的研究观点和趋势\n\n【故事素材挖掘】：\n- 人物故事：相关的典型人物和经历\n- 历史事件：重要的历史事件和转折点\n- 现实案例：当前的实际案例和应用\n- 未来场景：可能的未来发展场景\n\n【跨领域连接】：\n基于用户专业背景，建立跨领域连接：\n- 相似模式：其他领域的相似现象\n- 可借鉴经验：其他领域的成功经验\n- 类比思维：可以类比的概念和模式\n- 迁移应用：知识和方法的迁移应用\n\n【素材质量评估】：\n为每类素材评估：\n- 权威性：信息来源的权威程度\n- 时效性：信息的新鲜度和时效性\n- 相关性：与话题的相关程度\n- 独特性：素材的独特价值\n\n【使用建议】：\n- 核心素材：必须使用的核心素材\n- 支撑素材：用于论证的支撑素材\n- 丰富素材：用于丰富内容的素材\n- 创新素材：体现独特视角的素材\n\n要求：素材丰富、来源可靠、逻辑清晰、支撑有力。"}, "inputs": ["optimized_style", "topic"], "outputs": ["integrated_knowledge"]}, {"id": "title_generator", "type": "llm", "name": "标题生成器", "config": {"model": "claude-3-sonnet", "temperature": 0.8, "max_tokens": 800, "prompt": "你是标题创作专家。基于内容分析，创作吸引人的标题：\n\n内容分析：{{integrated_knowledge}}\n个人风格：{{optimized_style}}\n目标受众：{{target_audience}}\n\n请创作3个不同类型的标题：\n\n【悬念式标题】：\n- 制造悬念，引发好奇心\n- 使用疑问句或省略号\n- 暗示有重要信息或秘密\n- 例如：\"为什么90%的人都不知道这个真相？\"\n\n【对比式标题】：\n- 突出对比和冲突\n- 使用\"VS\"、\"却\"、\"但是\"等词\n- 制造认知反差\n- 例如：\"人人都在追捧AI，但这个风险却被忽视了\"\n\n【价值式标题】：\n- 直接体现价值和收益\n- 使用数字和具体描述\n- 明确告诉读者能获得什么\n- 例如：\"3个方法让你的工作效率提升50%\"\n\n每个标题要求：\n- 15-25字最佳长度\n- 包含核心关键词\n- 符合今日头条平台特点\n- 体现个人风格特色\n- 具有强烈的点击欲望\n\n输出格式：\n标题类型：具体标题\n预期效果：说明为什么这个标题有吸引力\n适用场景：什么情况下使用这个标题\n\n要求：标题新颖、吸引力强、符合平台规范。"}, "inputs": ["integrated_knowledge", "optimized_style", "target_audience"], "outputs": ["generated_titles"]}, {"id": "content_structure_designer", "type": "llm", "name": "内容结构设计器", "config": {"model": "gpt-4-turbo", "temperature": 0.4, "max_tokens": 2000, "prompt": "你是内容结构专家。基于分析结果，设计文章的整体结构：\n\n知识整合：{{integrated_knowledge}}\n个人风格：{{optimized_style}}\n独特角度：{{unique_angles}}\n生成标题：{{generated_titles}}\n\n请设计完整的文章结构：\n\n【开头设计】（200-300字）：\n- 开头方式：选择最适合的开头方式（故事/数据/问题/场景）\n- 核心钩子：吸引读者继续阅读的关键点\n- 价值预告：告诉读者将获得什么价值\n- 情感连接：与读者建立初步情感联系\n\n【主体结构】（1500-2000字）：\n\n第一部分：现状分析\n- 核心观点：要表达的主要观点\n- 支撑论据：3-5个有力的论据\n- 案例素材：1-2个典型案例\n- 个人见解：基于专业背景的独特见解\n\n第二部分：深度剖析\n- 本质挖掘：问题或现象的本质\n- 多维分析：从不同角度的分析\n- 跨界思考：结合其他领域的思考\n- 前瞻预测：对未来的预测和判断\n\n第三部分：价值输出\n- 实用建议：具体可行的建议\n- 行动指南：读者可以采取的行动\n- 思维启发：对读者思维的启发\n- 情感共鸣：与读者的情感共鸣点\n\n【结尾设计】（200-300字）：\n- 观点总结：核心观点的简洁总结\n- 价值升华：将话题升华到更高层面\n- 情感表达：真实的情感表达\n- 互动引导：引导读者思考或行动\n\n【个性化元素】：\n- 个人经历融入点：在哪些地方融入个人经历\n- 专业见解体现：如何体现专业背景\n- 情感表达节点：在哪些地方表达真实情感\n- 读者互动设计：如何与读者产生互动\n\n【写作要点】：\n- 逻辑主线：整篇文章的逻辑主线\n- 过渡衔接：各部分之间的过渡方式\n- 节奏控制：内容的节奏和起伏\n- 亮点设置：文章的几个亮点和高潮\n\n要求：结构清晰、逻辑严密、有个人特色、具有吸引力。"}, "inputs": ["integrated_knowledge", "optimized_style", "unique_angles", "generated_titles"], "outputs": ["content_structure"]}, {"id": "main_content_generator", "type": "llm", "name": "主要内容生成器", "config": {"model": "gpt-4-turbo", "temperature": 0.6, "max_tokens": 4500, "prompt": "你是专业内容创作大师。基于结构设计，生成高质量的文章内容：\n\n内容结构：{{content_structure}}\n知识素材：{{integrated_knowledge}}\n个人风格：{{optimized_style}}\n独特角度：{{unique_angles}}\n目标受众：{{target_audience}}\n\n请按照设计的结构创作完整文章：\n\n【标题创作】：\n运用以下技巧创作3个备选标题：\n1. 悬念式：制造悬念，引发好奇\n2. 对比式：突出对比，制造冲突\n3. 价值式：直接体现价值，吸引目标受众\n\n每个标题要求：\n- 15-25字最佳长度\n- 包含关键词和情感词\n- 体现独特角度\n- 符合个人风格\n\n【内容创作】：\n\n开头段（吸引注意）：\n- 运用个人化开头方式\n- 快速建立与读者的连接\n- 明确文章价值和独特性\n- 设置悬念或提出问题\n\n主体内容（深度展开）：\n按照个人风格结构展开，包含：\n\n观点阐述：\n- 明确表达独特观点\n- 提供有力论证支撑\n- 运用个人专业背景\n- 体现深度思考过程\n\n案例分析：\n- 选择典型案例进行分析\n- 结合个人经历和感悟\n- 提供多角度解读\n- 突出独特见解\n\n深度思考：\n- 挖掘问题本质\n- 提供前瞻性分析\n- 连接更大的社会意义\n- 体现个人价值观\n\n实用价值：\n- 为读者提供具体建议\n- 分享可操作的方法\n- 给出明确的行动指南\n- 帮助读者解决实际问题\n\n结尾段（升华总结）：\n- 总结核心观点\n- 升华文章主题\n- 与读者建立情感共鸣\n- 引导思考或行动\n\n【个性化元素融入】：\n- 个人经历：自然融入相关个人经历\n- 专业见解：体现专业背景的独特见解\n- 情感表达：真实的情感和感悟\n- 价值观输出：个人价值观的自然体现\n- 互动设计：引发读者思考和讨论的元素\n\n【差异化特色】：\n- 独特视角：与众不同的分析角度\n- 深度思考：超越表面的深层分析\n- 创新表达：新颖的表达方式和结构\n- 情感共鸣：真实的情感连接\n- 实用价值：对读者的实际帮助\n\n【质量要求】：\n- 字数：2000-3000字\n- 逻辑：清晰的逻辑结构\n- 深度：有深度的分析和思考\n- 创意：有创意的表达和观点\n- 情感：有情感的真实表达\n- 价值：有价值的内容输出\n\n要求：内容原创、观点独特、情感真实、价值突出、具有强烈的个人特色。"}, "inputs": ["content_structure", "integrated_knowledge", "optimized_style", "unique_angles", "target_audience"], "outputs": ["main_content"]}, {"id": "humanization_processor", "type": "llm", "name": "人性化处理器", "config": {"model": "claude-3-sonnet", "temperature": 0.8, "max_tokens": 3500, "prompt": "你是内容人性化专家。将AI生成的内容转化为更自然、更有人情味的表达：\n\n原始内容：{{main_content}}\n个人风格：{{optimized_style}}\n用户背景：{{user_background}}\n\n请进行全面的人性化处理：\n\n【语言自然化】：\n- 口语化表达：将书面语转化为更自然的口语表达\n- 情感词汇：增加情感色彩丰富的词汇\n- 语气变化：根据内容调整语气（亲切/严肃/幽默/感慨）\n- 节奏调整：长短句结合，形成自然的语言节奏\n\n【个人化元素增强】：\n- 真实感受：添加真实的个人感受和体验\n- 生活细节：融入生活化的细节和场景\n- 情感表达：增加真实的情感流露\n- 个人观点：强化个人独特的观点和立场\n\n【互动性提升】：\n- 直接对话：使用\"你\"、\"我们\"等拉近距离\n- 提问引导：适当提出问题引发思考\n- 共鸣点设置：设置容易产生共鸣的内容点\n- 互动邀请：邀请读者参与讨论或分享\n\n【表达多样化】：\n- 修辞手法：适当使用比喻、排比、反问等\n- 表达方式：叙述、描写、议论、抒情相结合\n- 视角切换：第一人称、第二人称灵活运用\n- 语言风格：正式与非正式语言的恰当搭配\n\n【真实性增强】：\n- 不完美表达：保留一些不那么完美的表达\n- 思考过程：展现思考和判断的过程\n- 犹豫和确定：适当表现犹豫和坚定\n- 个人局限：承认个人认知的局限性\n\n【情感层次丰富】：\n- 情感起伏：内容要有情感的起伏变化\n- 真实情感：表达真实的喜怒哀乐\n- 情感共鸣：与读者建立情感连接\n- 情感升华：在适当时候进行情感升华\n\n要求：保持原有观点和逻辑，增强人性化表达，降低AI痕迹，提升真实感和亲和力。"}, "inputs": ["main_content", "optimized_style", "user_background"], "outputs": ["humanized_content"]}, {"id": "quality_checker", "type": "llm", "name": "质量检查器", "config": {"model": "gpt-4-turbo", "temperature": 0.2, "max_tokens": 2000, "prompt": "你是内容质量专家。对文章进行全面的质量检查和优化建议：\n\n文章内容：{{humanized_content}}\n原始要求：{{topic}} - {{target_audience}}\n个人风格：{{optimized_style}}\n\n请进行全面质量评估：\n\n【内容质量评估】：\n1. 原创性检查（1-10分）：\n   - 观点独特性\n   - 表达原创性\n   - 避免常见套路\n   - 个人特色体现\n\n2. 逻辑性检查（1-10分）：\n   - 论证逻辑清晰\n   - 结构层次分明\n   - 前后呼应完整\n   - 过渡自然流畅\n\n3. 深度性检查（1-10分）：\n   - 分析深入透彻\n   - 见解独到深刻\n   - 思考层次丰富\n   - 价值输出明确\n\n4. 可读性检查（1-10分）：\n   - 语言表达自然\n   - 节奏把握恰当\n   - 易于理解接受\n   - 吸引力和感染力\n\n【平台适配性检查】：\n- 标题吸引力：是否符合今日头条特点\n- 内容长度：字数是否合适（2000-3000字）\n- 段落设置：段落长度和分布是否合理\n- 关键词密度：关键词使用是否自然\n\n【差异化特色检查】：\n- 独特角度：是否体现了差异化视角\n- 个人标签：是否强化了个人IP特色\n- 竞争优势：相比同类内容的优势\n- 记忆点：是否有令人印象深刻的内容\n\n【改进建议】：\n针对发现的问题，提供具体的改进建议：\n- 内容调整建议\n- 表达优化建议\n- 结构改进建议\n- 特色强化建议\n\n【最终评分】：\n- 综合质量评分：（1-10分）\n- 发布建议：（立即发布/小幅修改后发布/需要大幅修改）\n- 预期表现：预测文章的可能表现\n\n要求：评估客观准确、建议具体可行、有助于提升内容质量。"}, "inputs": ["humanized_content", "topic", "target_audience", "optimized_style"], "outputs": ["quality_report"]}, {"id": "final_formatter", "type": "llm", "name": "最终格式化器", "config": {"model": "gpt-4", "temperature": 0.1, "max_tokens": 4000, "prompt": "你是内容格式化专家。将内容格式化为适合今日头条发布的最终版本：\n\n文章内容：{{humanized_content}}\n质量报告：{{quality_report}}\n生成标题：{{generated_titles}}\n\n请完成最终格式化：\n\n【标题优化】：\n从生成的标题中选择最佳标题，或基于质量报告进行微调：\n- 确保15-25字长度\n- 包含核心关键词\n- 具有强烈吸引力\n- 符合平台规范\n\n【内容格式化】：\n\n1. 段落优化：\n   - 每段控制在100-200字\n   - 重要观点独立成段\n   - 适当使用小标题\n   - 保持视觉层次清晰\n\n2. 标点符号优化：\n   - 规范使用标点符号\n   - 适当使用感叹号增强语气\n   - 合理使用省略号制造悬念\n   - 问号的恰当使用\n\n3. 排版优化：\n   - 重要内容加粗标注\n   - 关键数据突出显示\n   - 适当使用序号和列表\n   - 保持整体美观\n\n【平台特色元素】：\n- 话题标签：添加2-3个相关话题标签\n- 互动引导：在结尾添加互动引导语\n- 关键词优化：确保关键词自然分布\n- 可读性优化：确保手机阅读体验\n\n【最终检查】：\n- 字数统计：确保在合适范围内\n- 错别字检查：消除错别字和语法错误\n- 逻辑检查：确保逻辑完整流畅\n- 风格一致性：保持整体风格统一\n\n【输出格式】：\n标题：[最终标题]\n\n正文：\n[格式化后的完整文章内容]\n\n话题标签：#标签1 #标签2 #标签3\n\n发布建议：\n- 最佳发布时间\n- 预期表现分析\n- 后续优化建议\n\n要求：格式规范、易于阅读、符合平台特点、保持内容质量。"}, "inputs": ["humanized_content", "quality_report", "generated_titles"], "outputs": ["final_article"]}, {"id": "output_node", "type": "end", "name": "输出节点", "config": {"output_fields": [{"name": "final_article", "type": "string", "description": "最终格式化的文章"}, {"name": "quality_report", "type": "string", "description": "质量评估报告"}, {"name": "trend_analysis", "type": "string", "description": "热点分析结果"}, {"name": "unique_angles", "type": "string", "description": "独特角度分析"}]}, "inputs": ["final_article", "quality_report", "trend_analysis", "unique_angles"]}], "connections": [{"from": "input_node", "to": "trend_discovery", "data_mapping": {"topic": "topic", "user_background": "user_background", "target_audience": "target_audience"}}, {"from": "trend_discovery", "to": "unique_angle_generator", "data_mapping": {"trend_analysis": "trend_analysis", "user_background": "user_background"}}, {"from": "unique_angle_generator", "to": "personal_style_optimizer", "data_mapping": {"unique_angles": "unique_angles", "user_background": "user_background", "content_style": "content_style"}}, {"from": "personal_style_optimizer", "to": "knowledge_integration", "data_mapping": {"optimized_style": "optimized_style", "topic": "topic"}}, {"from": "knowledge_integration", "to": "title_generator", "data_mapping": {"integrated_knowledge": "integrated_knowledge", "optimized_style": "optimized_style", "target_audience": "target_audience"}}, {"from": "title_generator", "to": "content_structure_designer", "data_mapping": {"generated_titles": "generated_titles", "integrated_knowledge": "integrated_knowledge", "optimized_style": "optimized_style", "unique_angles": "unique_angles"}}, {"from": "content_structure_designer", "to": "main_content_generator", "data_mapping": {"content_structure": "content_structure", "integrated_knowledge": "integrated_knowledge", "optimized_style": "optimized_style", "unique_angles": "unique_angles", "target_audience": "target_audience"}}, {"from": "main_content_generator", "to": "humanization_processor", "data_mapping": {"main_content": "main_content", "optimized_style": "optimized_style", "user_background": "user_background"}}, {"from": "humanization_processor", "to": "quality_checker", "data_mapping": {"humanized_content": "humanized_content", "topic": "topic", "target_audience": "target_audience", "optimized_style": "optimized_style"}}, {"from": "quality_checker", "to": "final_formatter", "data_mapping": {"quality_report": "quality_report", "humanized_content": "humanized_content", "generated_titles": "generated_titles"}}, {"from": "final_formatter", "to": "output_node", "data_mapping": {"final_article": "final_article", "quality_report": "quality_report", "trend_analysis": "trend_analysis", "unique_angles": "unique_angles"}}]}