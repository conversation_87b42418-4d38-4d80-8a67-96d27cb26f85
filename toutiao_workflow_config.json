{"workflow_name": "今日头条高质量文章生成工作流", "description": "自动化生成高质量、原创度高、AI检测率低的今日头条文章", "nodes": [{"id": "input_node", "type": "input", "name": "文章主题输入", "description": "用户输入文章主题或关键词", "config": {"input_fields": [{"name": "topic", "type": "text", "label": "文章主题", "required": true, "placeholder": "请输入文章主题，如：人工智能发展趋势"}, {"name": "target_audience", "type": "select", "label": "目标受众", "options": ["科技爱好者", "职场人士", "学生群体", "中老年人", "创业者", "投资者"], "default": "科技爱好者"}, {"name": "article_style", "type": "select", "label": "文章风格", "options": ["深度分析", "轻松科普", "观点评论", "实用指南", "故事叙述"], "default": "深度分析"}]}, "outputs": ["topic", "target_audience", "article_style"]}, {"id": "research_node", "type": "llm", "name": "主题研究与素材收集", "description": "深度研究主题，收集相关素材和数据", "config": {"model": "gpt-4", "temperature": 0.3, "max_tokens": 2000, "prompt": "你是一位资深的内容研究专家。请基于用户提供的主题进行深度研究：\n\n主题：{{topic}}\n目标受众：{{target_audience}}\n文章风格：{{article_style}}\n\n请完成以下任务：\n1. 分析当前该主题的热点和趋势\n2. 收集3-5个相关的真实案例或数据\n3. 识别目标受众最关心的痛点\n4. 提供独特的观点角度\n5. 列出可能的争议点或讨论点\n\n输出格式：\n【热点趋势】：\n【真实案例】：\n【受众痛点】：\n【独特角度】：\n【争议讨论点】：\n\n要求：信息准确、观点新颖、贴近受众需求。"}, "inputs": ["topic", "target_audience", "article_style"], "outputs": ["research_content"]}, {"id": "outline_node", "type": "llm", "name": "文章大纲设计", "description": "基于研究内容设计文章结构大纲", "config": {"model": "gpt-4", "temperature": 0.4, "max_tokens": 1500, "prompt": "你是一位经验丰富的内容策划师。基于研究内容，为今日头条平台设计一个吸引人的文章大纲：\n\n研究内容：{{research_content}}\n主题：{{topic}}\n目标受众：{{target_audience}}\n文章风格：{{article_style}}\n\n请设计文章大纲，包含：\n1. 3个备选标题（要求：吸引眼球、包含关键词、符合头条特点）\n2. 文章结构（6-8个段落）\n3. 每个段落的核心要点\n4. 图片插入位置建议\n5. 互动元素设计（提问、投票等）\n\n输出格式：\n【备选标题】：\n1. \n2. \n3. \n\n【文章结构】：\n开头段：\n主体段1：\n主体段2：\n...\n结尾段：\n\n【图片位置】：\n【互动元素】：\n\n要求：结构清晰、逻辑性强、适合移动端阅读。"}, "inputs": ["research_content", "topic", "target_audience", "article_style"], "outputs": ["article_outline"]}, {"id": "title_optimize_node", "type": "llm", "name": "标题优化与选择", "description": "优化标题，提高点击率和传播性", "config": {"model": "gpt-4", "temperature": 0.6, "max_tokens": 800, "prompt": "你是今日头条的资深标题优化师。基于文章大纲，优化标题以提高点击率：\n\n文章大纲：{{article_outline}}\n主题：{{topic}}\n\n请完成：\n1. 从大纲中选择最佳标题\n2. 进一步优化该标题（考虑SEO、情感触发、数字化等）\n3. 提供2个优化版本\n4. 分析每个标题的优势\n\n标题优化原则：\n- 15-25字最佳\n- 包含情感词汇\n- 制造悬念或冲突\n- 避免标题党\n- 符合平台规范\n\n输出格式：\n【最终标题】：\n【备选标题1】：\n【备选标题2】：\n【标题分析】：\n\n要求：标题既要吸引点击，又要真实反映内容。"}, "inputs": ["article_outline", "topic"], "outputs": ["optimized_title"]}, {"id": "content_generation_node", "type": "llm", "name": "文章内容生成", "description": "生成高质量、人性化的文章内容", "config": {"model": "gpt-4", "temperature": 0.7, "max_tokens": 4000, "prompt": "你是一位资深的自媒体作者，擅长写出有血有肉、富有情感的文章。请基于大纲生成完整文章：\n\n文章大纲：{{article_outline}}\n优化标题：{{optimized_title}}\n研究内容：{{research_content}}\n目标受众：{{target_audience}}\n\n写作要求：\n1. 语言自然流畅，避免AI痕迹\n2. 融入个人观点和情感\n3. 使用真实案例和数据\n4. 适当使用口语化表达\n5. 段落间逻辑清晰\n6. 字数控制在1500-2500字\n\n人性化技巧：\n- 使用第一人称分享经历\n- 加入个人思考和感悟\n- 适当使用反问句\n- 融入生活化比喻\n- 表达真实情感\n\n输出格式：\n【标题】：\n【正文】：\n（请按段落分别输出，每段标注段落号）\n\n要求：内容原创、观点独特、情感真实、可读性强。"}, "inputs": ["article_outline", "optimized_title", "research_content", "target_audience"], "outputs": ["article_content"]}, {"id": "image_planning_node", "type": "llm", "name": "图片规划与描述", "description": "规划文章配图，生成图片描述", "config": {"model": "gpt-4", "temperature": 0.5, "max_tokens": 1200, "prompt": "你是专业的视觉内容策划师。基于文章内容规划配图方案：\n\n文章内容：{{article_content}}\n文章标题：{{optimized_title}}\n\n请完成：\n1. 确定需要配图的位置（3-6张）\n2. 为每张图片写详细描述\n3. 指定图片类型和风格\n4. 考虑图文语义匹配度\n\n图片类型：\n- 封面图：吸引眼球，体现主题\n- 内容图：配合文字，增强理解\n- 数据图：图表、统计数据可视化\n- 情感图：烘托氛围，增强共鸣\n\n输出格式：\n【图片1-封面图】：\n位置：文章开头\n描述：\n风格：\n语义匹配：\n\n【图片2】：\n位置：\n描述：\n风格：\n语义匹配：\n\n（继续其他图片...）\n\n要求：图片与内容高度相关，提升阅读体验。"}, "inputs": ["article_content", "optimized_title"], "outputs": ["image_plan"]}, {"id": "humanization_node", "type": "llm", "name": "内容人性化处理", "description": "进一步优化内容，降低AI检测率", "config": {"model": "gpt-4", "temperature": 0.8, "max_tokens": 3000, "prompt": "你是内容人性化专家。请对文章进行深度人性化处理，降低AI检测率：\n\n原文章：{{article_content}}\n图片规划：{{image_plan}}\n\n人性化处理要点：\n1. 语言风格调整\n   - 增加口语化表达\n   - 使用不规则句式\n   - 加入语气词和感叹\n   - 适当使用网络用语\n\n2. 内容结构优化\n   - 调整段落长短\n   - 增加过渡句\n   - 加入个人经历\n   - 融入真实感受\n\n3. 情感表达增强\n   - 加入个人观点\n   - 表达真实情感\n   - 使用生动比喻\n   - 增加互动元素\n\n4. 细节丰富化\n   - 补充具体细节\n   - 加入时间地点\n   - 使用具体数字\n   - 增加感官描述\n\n输出格式：\n【人性化标题】：\n【人性化正文】：\n（保持段落结构，标注图片插入位置）\n\n要求：保持原意不变，大幅提升人性化程度。"}, "inputs": ["article_content", "image_plan"], "outputs": ["humanized_content"]}, {"id": "quality_check_node", "type": "llm", "name": "质量检查与优化", "description": "全面检查文章质量，进行最终优化", "config": {"model": "gpt-4", "temperature": 0.3, "max_tokens": 2000, "prompt": "你是资深的内容质量审核专家。请全面检查文章质量：\n\n文章内容：{{humanized_content}}\n原始主题：{{topic}}\n目标受众：{{target_audience}}\n\n检查维度：\n1. 内容质量\n   - 逻辑是否清晰\n   - 观点是否新颖\n   - 信息是否准确\n   - 价值是否突出\n\n2. 可读性\n   - 语言是否流畅\n   - 结构是否合理\n   - 节奏是否适中\n   - 是否易于理解\n\n3. 平台适配\n   - 是否符合头条特点\n   - 标题是否吸引人\n   - 长度是否合适\n   - 是否适合移动端\n\n4. 原创性\n   - AI痕迹是否明显\n   - 是否有个人特色\n   - 情感是否真实\n   - 观点是否独特\n\n输出格式：\n【质量评分】：（1-10分）\n【优化建议】：\n【最终版本】：\n（如需修改则提供，否则说明无需修改）\n\n要求：严格把关，确保高质量输出。"}, "inputs": ["humanized_content", "topic", "target_audience"], "outputs": ["final_content", "quality_score"]}, {"id": "formatting_node", "type": "llm", "name": "排版格式化", "description": "进行最终的排版和格式化", "config": {"model": "gpt-4", "temperature": 0.2, "max_tokens": 2500, "prompt": "你是专业的内容排版师。请对文章进行最终排版：\n\n文章内容：{{final_content}}\n图片规划：{{image_plan}}\n质量评分：{{quality_score}}\n\n排版要求：\n1. 标题格式化\n   - 主标题突出显示\n   - 副标题层次清晰\n\n2. 段落优化\n   - 段落间距适中\n   - 重点内容突出\n   - 关键词加粗\n\n3. 图片标注\n   - 明确插入位置\n   - 添加图片说明\n   - 标注图片尺寸建议\n\n4. 互动元素\n   - 添加话题标签\n   - 设置互动问题\n   - 引导用户参与\n\n5. 移动端适配\n   - 控制段落长度\n   - 优化阅读体验\n   - 确保加载速度\n\n输出格式：\n【最终标题】：\n【话题标签】：#标签1# #标签2# #标签3#\n【正文内容】：\n（包含完整排版和图片位置标注）\n【互动问题】：\n【发布建议】：\n\n要求：排版美观、适合今日头条平台发布。"}, "inputs": ["final_content", "image_plan", "quality_score"], "outputs": ["formatted_article"]}, {"id": "output_node", "type": "output", "name": "最终输出", "description": "输出完整的文章内容和发布指南", "config": {"output_fields": [{"name": "final_article", "type": "text", "label": "完整文章", "source": "formatted_article"}, {"name": "publish_guide", "type": "text", "label": "发布指南", "value": "1. 复制文章内容到今日头条编辑器\n2. 按照标注位置插入图片\n3. 添加话题标签\n4. 设置发布时间（建议早上8-9点或晚上7-8点）\n5. 选择合适的分类\n6. 开启评论功能，及时互动"}]}, "inputs": ["formatted_article"], "outputs": []}], "workflow_connections": [{"from": "input_node", "to": "research_node", "data_mapping": {"topic": "topic", "target_audience": "target_audience", "article_style": "article_style"}}, {"from": "research_node", "to": "outline_node", "data_mapping": {"research_content": "research_content"}}, {"from": "outline_node", "to": "title_optimize_node", "data_mapping": {"article_outline": "article_outline"}}, {"from": "title_optimize_node", "to": "content_generation_node", "data_mapping": {"optimized_title": "optimized_title"}}, {"from": "content_generation_node", "to": "image_planning_node", "data_mapping": {"article_content": "article_content"}}, {"from": "image_planning_node", "to": "humanization_node", "data_mapping": {"image_plan": "image_plan"}}, {"from": "humanization_node", "to": "quality_check_node", "data_mapping": {"humanized_content": "humanized_content"}}, {"from": "quality_check_node", "to": "formatting_node", "data_mapping": {"final_content": "final_content", "quality_score": "quality_score"}}, {"from": "formatting_node", "to": "output_node", "data_mapping": {"formatted_article": "formatted_article"}}], "workflow_settings": {"auto_run": false, "save_intermediate_results": true, "error_handling": "continue_on_error", "timeout_seconds": 300}}